# src/agents/search_agent.py
from datetime import datetime, timedelta
import dateparser   # NEW: install with `pip install dateparser`
from src.tools import db


def _parse_date(nl_date: str) -> str:
    """
    Parse a natural language date (e.g. 'this Friday', 'next weekend')
    into a YYYY-MM-DD string.
    Falls back to today if parsing fails.
    """
    if not nl_date:
        return datetime.today().strftime("%Y-%m-%d")

    parsed = dateparser.parse(nl_date, settings={"PREFER_DATES_FROM": "future"})
    if parsed:
        return parsed.strftime("%Y-%m-%d")
    return datetime.today().strftime("%Y-%m-%d")


def search(intent_info: dict):
    results = {}
    intent = intent_info.get("intent")

    if intent in ["flight", "both"]:
        origin = intent_info.get("origin", "Mumbai")
        destination = intent_info.get("destination", "Delhi")
        date_str = _parse_date(intent_info.get("date", "today"))
        results["flights"] = db.find_flights(origin, destination, date_str)

    if intent in ["hotel", "both"]:
        city = intent_info.get("city", "Goa")
        checkin_str = _parse_date(intent_info.get("checkin", "today"))
        # assume 2 nights by default
        checkout_str = (datetime.strptime(checkin_str, "%Y-%m-%d") + timedelta(days=2)).strftime("%Y-%m-%d")
        results["hotels"] = db.find_hotels(city, checkin_str, checkout_str)

    return results

