from typing import TypedDict
from langgraph.graph import StateGraph, END
from .agents.intent_agent import parse_intent
from .agents.summary_agent import SummaryAgent
from .agents.quote_agent import build_quote
from .agents import api_agent
from .tools import db
import os


class TravelState(TypedDict, total=False):
    user_input: str
    intent_info: dict
    search_payload: dict
    summary: str
    selection: dict
    customer: str
    quotation_path: str


# Check .env for search mode
USE_API = os.getenv("USE_API", "false").lower() == "true"


def node_parse(state: TravelState) -> TravelState:
    return {"intent_info": parse_intent(state["user_input"])}


def node_search(state: TravelState) -> TravelState:
    info = state["intent_info"]
    results = {}

    if info.get("flight"):
        if USE_API:
            results["flights"] = api_agent.api_find_flights(
                info["origin"], info["destination"], info["date"]
            )
        else:
            results["flights"] = db.find_flights(
                info["origin"], info["destination"], info["date"]
            )

    if info.get("hotel"):
        if USE_API:
            results["hotels"] = api_agent.api_find_hotels(
                info["city"], info["checkin"], info["checkout"]
            )
        else:
            results["hotels"] = db.find_hotels(
                info["city"], info["checkin"], info["checkout"]
            )

    return {"search_payload": results}


def node_summary(state: TravelState) -> TravelState:
    agent = SummaryAgent()
    intent = state["intent_info"].get("intent", "unknown")
    flights = state["search_payload"].get("flights", [])
    hotels = state["search_payload"].get("hotels", [])
    return {"summary": agent.summarize(intent, flights, hotels)}


def node_quote(state: TravelState) -> TravelState:
    sel = state.get("selection", {})
    return {
        "quotation_path": build_quote(
            sel.get("flight"), sel.get("hotel"), state.get("customer", "Customer")
        )
    }


def build_workflow(include_quote=False):
    g = StateGraph(TravelState)
    g.add_node("parse", node_parse)
    g.add_node("search", node_search)
    g.add_node("summary", node_summary)
    g.add_node("quote", node_quote)

    g.set_entry_point("parse")
    g.add_edge("parse", "search")
    g.add_edge("search", "summary")

    if include_quote:
        g.add_edge("summary", "quote")
        g.add_edge("quote", END)
    else:
        g.add_edge("summary", END)

    return g.compile()
