import argparse
from .graph import build_workflow

def run_query(user_input):
    app=build_workflow(False)
    out=app.invoke({"user_input":user_input})
    print(out["summary"])

def run_selection(flight_id,hotel_id,customer):
    app=build_workflow(True)
    sel={}
    if flight_id: sel["flight"]={"id":flight_id,"price":5000,"currency":"INR"}
    if hotel_id: sel["hotel"]={"id":hotel_id,"price_per_night":3000,"currency":"INR","name":"HotelX"}
    out=app.invoke({"user_input":"sel","selection":sel,"customer":customer})
    print("Quotation at",out["quotation_path"])

def main():
    p=argparse.ArgumentParser()
    p.add_argument("--input",type=str)
    p.add_argument("--select-flight")
    p.add_argument("--select-hotel")
    p.add_argument("--customer")
    a=p.parse_args()
    if a.input: run_query(a.input)
    elif a.select_flight or a.select_hotel: run_selection(a.select_flight,a.select_hotel,a.customer)

if __name__=="__main__": main()
