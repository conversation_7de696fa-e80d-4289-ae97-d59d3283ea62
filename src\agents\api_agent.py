import os
import requests

MOCK_API_URL = os.getenv("MOCK_API_URL", "http://127.0.0.1:8001")

def api_find_flights(origin, destination, depart_date):
    url = f"{MOCK_API_URL}/flights"
    resp = requests.get(url, params={"origin": origin, "destination": destination, "depart_date": depart_date})
    return resp.json() if resp.status_code == 200 else []

def api_find_hotels(city, checkin, checkout):
    url = f"{MOCK_API_URL}/hotels"
    resp = requests.get(url, params={"city": city, "checkin": checkin, "checkout": checkout})
    return resp.json() if resp.status_code == 200 else []
