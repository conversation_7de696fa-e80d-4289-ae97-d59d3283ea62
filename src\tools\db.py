import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv
import datetime

# Load environment variables
load_dotenv()

def get_engine():
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        raise RuntimeError("DATABASE_URL is not set")
    return create_engine(db_url, future=True)

# ------------------ Flights ------------------
def find_flights(origin, destination, depart_date):
    """
    Find flights with ±3 day flexibility around the given date.
    """
    engine = get_engine()
    date_obj = datetime.datetime.strptime(depart_date, "%Y-%m-%d").date()
    start_date = (date_obj - datetime.timedelta(days=3)).isoformat()
    end_date   = (date_obj + datetime.timedelta(days=3)).isoformat()

    with engine.connect() as conn:
        res = conn.execute(
            text("""
                SELECT * FROM flights
                WHERE origin=:o AND destination=:d
                AND depart_date BETWEEN :start AND :end
                ORDER BY depart_date, depart_time
            """),
            {"o": origin, "d": destination, "start": start_date, "end": end_date}
        ).mappings().all()

        return [dict(r) for r in res]

# ------------------ Hotels ------------------
def find_hotels(city, checkin, checkout):
    """
    Find hotels with ±3 day flexibility for check-in/check-out dates.
    """
    engine = get_engine()
    ci_obj = datetime.datetime.strptime(checkin, "%Y-%m-%d").date()
    co_obj = datetime.datetime.strptime(checkout, "%Y-%m-%d").date()

    start_ci = (ci_obj - datetime.timedelta(days=3)).isoformat()
    end_ci   = (ci_obj + datetime.timedelta(days=3)).isoformat()
    start_co = (co_obj - datetime.timedelta(days=3)).isoformat()
    end_co   = (co_obj + datetime.timedelta(days=3)).isoformat()

    with engine.connect() as conn:
        res = conn.execute(
            text("""
                SELECT * FROM hotels
                WHERE city=:c
                AND checkin_date BETWEEN :ci_start AND :ci_end
                AND checkout_date BETWEEN :co_start AND :co_end
                ORDER BY checkin_date
            """),
            {
                "c": city,
                "ci_start": start_ci, "ci_end": end_ci,
                "co_start": start_co, "co_end": end_co
            }
        ).mappings().all()

        return [dict(r) for r in res]
