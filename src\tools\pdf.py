from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from datetime import datetime

def generate_quotation_pdf(path, customer, flight=None, hotel=None):
    c = canvas.Canvas(path, pagesize=A4)
    width, height = A4
    y = height - 50
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50,y,"Travel Quotation")
    y -= 40
    c.setFont("Helvetica", 12)
    c.drawString(50,y,f"Customer: {customer}")
    y -= 20
    c.drawString(50,y,f"Generated: {datetime.now()}")
    y -= 40
    total = 0
    if flight:
        c.drawString(50,y,"Flight: "+str(flight))
        total += float(flight.get("price",0))
        y -= 40
    if hotel:
        c.drawString(50,y,"Hotel: "+str(hotel))
        total += float(hotel.get("price_per_night",0))
        y -= 40
    c.setFont("Helvetica-Bold", 14)
    c.drawString(50,y,f"Total: {total} INR")
    c.save()
    return path
