# src/agents/summary_agent.py

"""
Summary Agent (Offline version)

Instead of using OpenAI LLM, this agent just formats the raw results
from the database into a simple, human-friendly summary.
"""

from typing import List, Dict


class SummaryAgent:
    def summarize_flights(self, flights: List[Dict]) -> str:
        if not flights:
            return "No flights found for your search."
        lines = ["Here are the available flights:\n"]
        for f in flights:
            lines.append(
                f"- {f['airline']} flight {f['id']} from {f['origin']} to {f['destination']} "
                f"on {f['depart_date']} at {f.get('depart_time','--')} → "
                f"₹{f['price']} ({f['seats_left']} seats left)"
            )
        return "\n".join(lines)

    def summarize_hotels(self, hotels: List[Dict]) -> str:
        if not hotels:
            return "No hotels found for your search."
        lines = ["Here are the available hotels:\n"]
        for h in hotels:
            lines.append(
                f"- {h['name']} ({h['stars']}⭐) in {h['city']} "
                f"from {h['checkin_date']} to {h['checkout_date']} "
                f"→ ₹{h['price_per_night']}/night ({h['rooms_left']} rooms left)"
            )
        return "\n".join(lines)

    def summarize(self, intent: str, flights: List[Dict], hotels: List[Dict]) -> str:
        if intent == "flight":
            return self.summarize_flights(flights)
        elif intent == "hotel":
            return self.summarize_hotels(hotels)
        elif intent == "both":
            return self.summarize_flights(flights) + "\n\n" + self.summarize_hotels(hotels)
        else:
            return "Sorry, I couldn't understand your request."
