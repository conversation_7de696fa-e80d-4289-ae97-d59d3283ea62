from fastapi import FastAPI
from typing import List, Dict

app = FastAPI()

# Mocked flights
FLIGHTS = [
    {"id": "F-MUM-DEL-201", "origin": "Mumbai", "destination": "Delhi", "depart_date": "2025-08-22",
     "airline": "Vistara", "price": 7200, "currency": "INR", "depart_time": "07:00", "arrive_time": "08:55", "seats_left": 6},
    {"id": "F-BLR-GOA-301", "origin": "Bangalore", "destination": "Goa", "depart_date": "2025-08-23",
     "airline": "SpiceJet", "price": 4100, "currency": "INR", "depart_time": "17:00", "arrive_time": "18:10", "seats_left": 10}
]

# Mocked hotels
HOTELS = [
    {"id": "H-DEL-401", "city": "Delhi", "checkin_date": "2025-08-22", "checkout_date": "2025-08-24",
     "name": "Capital Inn", "stars": 3, "price_per_night": 3500, "currency": "INR", "rooms_left": 5},
    {"id": "H-GOA-501", "city": "Goa", "checkin_date": "2025-08-23", "checkout_date": "2025-08-26",
     "name": "Beachside Paradise", "stars": 4, "price_per_night": 5400, "currency": "INR", "rooms_left": 8}
]

@app.get("/flights")
def get_flights(origin: str, destination: str, depart_date: str) -> List[Dict]:
    return [f for f in FLIGHTS if f["origin"] == origin and f["destination"] == destination and f["depart_date"] == depart_date]

@app.get("/hotels")
def get_hotels(city: str, checkin: str, checkout: str) -> List[Dict]:
    return [h for h in HOTELS if h["city"] == city and h["checkin_date"] == checkin and h["checkout_date"] == checkout]
